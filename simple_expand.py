import csv
import random
import uuid
from datetime import datetime, timedelta

# 读取现有数据
encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']
existing_data = []
for encoding in encodings:
    try:
        with open('flag-40.csv', 'r', encoding=encoding) as f:
            reader = csv.reader(f)
            existing_data = list(reader)
        print(f"成功使用 {encoding} 编码读取文件")
        break
    except UnicodeDecodeError:
        continue
else:
    print("无法读取文件，尝试所有编码都失败")
    exit(1)

print(f"当前数据集包含 {len(existing_data) - 1} 条记录")

# 配置数据
departments = ['HR', 'IT', 'Sales', 'Customer Support', 'Product Management', 'Marketing', 'Operations']
categories = ['Assets', 'Services', 'Travel', 'Miscellaneous']
types = ['One-time', 'Recurring']
statuses = ['Processed', 'Pending', 'Submitted', 'Declined']
locations = ['Asia', 'Europe', 'North America', 'South America', 'Africa']
config_items = ['server', 'cloud', 'database', 'software', 'monitoring', 'security', 'infrastructure', 'network', 'equipment', 'service', 'asset', 'travel', 'compliance']
users = ['john_smith', 'mary_johnson', 'david_brown', 'sarah_davis', 'michael_wilson', 'lisa_anderson', 'robert_taylor', 'jennifer_thomas', 'william_jackson', 'elizabeth_white']

descriptions = [
    'System maintenance and upgrade request',
    'Software license renewal',
    'Hardware procurement',
    'Training and development program',
    'Consulting services engagement',
    'Travel and accommodation booking',
    'Office supplies and equipment',
    'Marketing campaign expenses',
    'Customer support tools',
    'Data backup and recovery'
]

def generate_record():
    # 生成创建时间（避开异常数据时间段）
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 12, 31)
    
    while True:
        create_time = start_date + timedelta(
            days=random.randint(0, (end_date - start_date).days),
            hours=random.randint(0, 23),
            minutes=random.randint(0, 59),
            seconds=random.randint(0, 59)
        )
        # 避开异常时间段
        if not ((create_time.month == 4 and 8 <= create_time.day <= 10) or 
                (create_time.month == 6 and 10 <= create_time.day <= 12)):
            break
    
    # 生成处理时间
    process_days = random.randint(1, 30)
    process_time = create_time + timedelta(days=process_days)
    
    # 生成金额（避免与异常数据重叠）
    amount = round(random.uniform(1000, 80000), 2)
    
    # 计算处理周期
    processing_cycle = max(1, (process_time - create_time).days)
    
    return [
        str(uuid.uuid4()),
        create_time.strftime('%Y-%m-%d %H:%M:%S'),
        amount,
        random.choice(statuses),
        random.choice(descriptions),
        random.choice(config_items),
        random.choice(users),
        random.choice(departments),
        random.choice(categories),
        process_time.strftime('%Y-%m-%d %H:%M:%S'),
        str(uuid.uuid4()),
        random.choice(types),
        random.choice(locations),
        processing_cycle
    ]

# 生成1000条新记录
new_records = []
for i in range(1000):
    if i % 100 == 0:
        print(f"已生成 {i} 条新记录...")
    new_records.append(generate_record())

print(f"成功生成 {len(new_records)} 条新记录")

# 合并数据
all_data = existing_data + new_records

# 写入文件
with open('flag-40.csv', 'w', newline='', encoding='utf-8-sig') as f:
    writer = csv.writer(f)
    writer.writerows(all_data)

print(f"数据集扩展完成！总记录数：{len(all_data) - 1}")
print("异常数据已保持不变，新数据已添加到数据集中")

# PowerShell脚本：扩展数据集
param(
    [int]$NewRecords = 1000
)

# 配置数据
$departments = @('HR', 'IT', 'Sales', 'Customer Support', 'Product Management', 'Marketing', 'Operations')
$categories = @('Assets', 'Services', 'Travel', 'Miscellaneous')
$types = @('One-time', 'Recurring')
$statuses = @('Processed', 'Pending', 'Submitted', 'Declined')
$locations = @('Asia', 'Europe', 'North America', 'South America', 'Africa')
$configItems = @('server', 'cloud', 'database', 'software', 'monitoring', 'security', 'infrastructure', 'network', 'equipment', 'service', 'asset', 'travel', 'compliance')
$users = @('john_smith', 'mary_johnson', 'david_brown', 'sarah_davis', 'michael_wilson', 'lisa_anderson', 'robert_taylor', 'jennifer_thomas', 'william_jackson', 'elizabeth_white', 'james_harris', 'patricia_martin', 'charles_thompson', 'linda_garcia', 'christopher_martinez', 'barbara_robinson', 'daniel_clark', 'helen_rodriguez', 'matthew_lewis', 'nancy_lee')

$descriptions = @(
    'System maintenance and upgrade request',
    'Software license renewal',
    'Hardware procurement',
    'Training and development program',
    'Consulting services engagement',
    'Travel and accommodation booking',
    'Office supplies and equipment',
    'Marketing campaign expenses',
    'Customer support tools',
    'Data backup and recovery',
    'Network infrastructure upgrade',
    'Security assessment and audit',
    'Business process optimization',
    'Employee benefits administration',
    'Vendor management services'
)

function New-Guid {
    return [System.Guid]::NewGuid().ToString()
}

function Get-RandomDate {
    param([DateTime]$Start, [DateTime]$End)
    $range = ($End - $Start).TotalDays
    $randomDays = Get-Random -Minimum 0 -Maximum $range
    return $Start.AddDays($randomDays).AddHours((Get-Random -Minimum 0 -Maximum 24)).AddMinutes((Get-Random -Minimum 0 -Maximum 60)).AddSeconds((Get-Random -Minimum 0 -Maximum 60))
}

function Get-RandomAmount {
    $ranges = @(
        @{Min=1000; Max=15000; Weight=40},
        @{Min=15000; Max=35000; Weight=30},
        @{Min=35000; Max=60000; Weight=20},
        @{Min=60000; Max=100000; Weight=10}
    )
    
    $rand = Get-Random -Minimum 1 -Maximum 101
    $cumulative = 0
    foreach ($range in $ranges) {
        $cumulative += $range.Weight
        if ($rand -le $cumulative) {
            return [Math]::Round((Get-Random -Minimum $range.Min -Maximum $range.Max), 2)
        }
    }
    return [Math]::Round((Get-Random -Minimum 1000 -Maximum 15000), 2)
}

Write-Host "开始扩展数据集，将添加 $NewRecords 条新记录..."

# 读取现有数据
$existingContent = Get-Content 'flag-40.csv' -Encoding UTF8
Write-Host "当前数据集包含 $($existingContent.Count - 1) 条记录"

# 生成新数据
$newRecords = @()
$startDate = Get-Date "2024-01-01"
$endDate = Get-Date "2024-12-31"

for ($i = 1; $i -le $NewRecords; $i++) {
    if ($i % 100 -eq 0) {
        Write-Host "已生成 $i 条新记录..."
    }
    
    # 生成创建时间（避开异常数据时间段）
    do {
        $createTime = Get-RandomDate -Start $startDate -End $endDate
    } while (($createTime.Month -eq 4 -and $createTime.Day -ge 8 -and $createTime.Day -le 10) -or 
             ($createTime.Month -eq 6 -and $createTime.Day -ge 10 -and $createTime.Day -le 12))
    
    # 生成处理时间
    $processDays = Get-Random -Minimum 1 -Maximum 31
    $processTime = $createTime.AddDays($processDays)
    
    # 计算处理周期
    $processingCycle = [Math]::Max(1, ($processTime - $createTime).Days)
    
    # 生成记录
    $record = @(
        (New-Guid),
        $createTime.ToString("yyyy-MM-dd HH:mm:ss"),
        (Get-RandomAmount),
        ($statuses | Get-Random),
        ($descriptions | Get-Random),
        ($configItems | Get-Random),
        ($users | Get-Random),
        ($departments | Get-Random),
        ($categories | Get-Random),
        $processTime.ToString("yyyy-MM-dd HH:mm:ss"),
        (New-Guid),
        ($types | Get-Random),
        ($locations | Get-Random),
        $processingCycle
    ) -join ','
    
    $newRecords += $record
}

Write-Host "成功生成 $($newRecords.Count) 条新记录"

# 合并数据
$allContent = $existingContent + $newRecords

# 写入文件
$allContent | Set-Content 'flag-40.csv' -Encoding UTF8

Write-Host "数据集扩展完成！总记录数：$($allContent.Count - 1)"
Write-Host "异常数据已保持不变，新数据已添加到数据集中"

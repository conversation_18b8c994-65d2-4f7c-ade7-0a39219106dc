#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2024年6月财务费用金额归因分析报告
分析6月相比5月的费用变化原因
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def generate_finance_attribution_report():
    """生成财务费用归因分析报告"""
    
    print("=" * 80)
    print("2024年6月财务费用金额归因分析报告")
    print("=" * 80)
    
    # 读取数据
    try:
        df = pd.read_csv('flag-40.csv', encoding='utf-8-sig')
    except FileNotFoundError:
        df = pd.read_csv('flag-40_2.csv', encoding='utf-8-sig')
    df['创建时间'] = pd.to_datetime(df['创建时间'])
    df['处理日期'] = pd.to_datetime(df['处理日期'])
    df['月份'] = df['创建时间'].dt.month
    df['年份'] = df['创建时间'].dt.year
    df['日期'] = df['创建时间'].dt.date
    
    # 筛选2024年5月和6月数据
    df_2024 = df[df['年份'] == 2024].copy()
    may_data = df_2024[df_2024['月份'] == 5].copy()
    june_data = df_2024[df_2024['月份'] == 6].copy()
    
    print(f"数据周期：观察期为2024年6月，对照期为2024年5月")
    print(f"数据范围：5月{len(may_data)}条记录，6月{len(june_data)}条记录")
    print()
    
    # 整体费用变化概览
    may_total = may_data['金额'].sum()
    june_total = june_data['金额'].sum()
    change_amount = june_total - may_total
    change_rate = (change_amount / may_total * 100) if may_total > 0 else 0
    
    print("【整体费用变化概览】")
    print(f"2024年5月总费用：{may_total:,.2f}元")
    print(f"2024年6月总费用：{june_total:,.2f}元")
    print(f"环比变化金额：{change_amount:,.2f}元")
    print(f"环比变化幅度：{change_rate:+.1f}%")
    print()
    
    # 1.1 费用结构变化分析
    print("=" * 60)
    print("1.1 费用结构变化分析")
    print("=" * 60)
    
    # 按部门分析
    may_dept = may_data.groupby('部门')['金额'].sum().round(2)
    june_dept = june_data.groupby('部门')['金额'].sum().round(2)
    
    # 合并部门数据
    dept_comparison = pd.DataFrame({
        '5月费用': may_dept,
        '6月费用': june_dept
    }).fillna(0)
    
    dept_comparison['变化金额'] = dept_comparison['6月费用'] - dept_comparison['5月费用']
    dept_comparison['环比变化率'] = (dept_comparison['变化金额'] / dept_comparison['5月费用'] * 100).replace([np.inf, -np.inf], 0)
    
    # 筛选环比增长最大的5个非零部门
    dept_growth = dept_comparison[dept_comparison['6月费用'] > 0].copy()
    top5_depts = dept_growth.nlargest(5, '变化金额')
    
    print("【按部门拆解费用金额 - TOP5环比增长部门】")
    print(f"{'部门':<15} {'本期值(6月)':<15} {'上期值(5月)':<15} {'变化金额':<15} {'环比变化率':<10}")
    print("-" * 80)
    
    for dept, row in top5_depts.iterrows():
        print(f"{dept:<15} {row['6月费用']:>12,.2f} {row['5月费用']:>12,.2f} {row['变化金额']:>12,.2f} {row['环比变化率']:>8.1f}%")
    
    print()
    
    # 对TOP5部门进行二级拆解
    print("【TOP5部门二级拆解归因】")
    
    for i, (dept, row) in enumerate(top5_depts.iterrows(), 1):
        print(f"\n{i}. {dept}部门详细分析")
        print("-" * 40)
        
        # 该部门5月和6月数据
        dept_may = may_data[may_data['部门'] == dept]
        dept_june = june_data[june_data['部门'] == dept]
        
        if len(dept_june) > 0:
            # 按类别拆解
            print("按类别拆解：")
            june_category = dept_june.groupby('类别')['金额'].sum().round(2)
            may_category = dept_may.groupby('类别')['金额'].sum().round(2) if len(dept_may) > 0 else pd.Series()
            
            for category, june_amount in june_category.items():
                may_amount = may_category.get(category, 0)
                change = june_amount - may_amount
                print(f"  {category}: 6月{june_amount:,.2f}元, 5月{may_amount:,.2f}元, 变化{change:+,.2f}元")
            
            # 按类型拆解
            print("按类型拆解：")
            june_type = dept_june.groupby('类型')['金额'].sum().round(2)
            may_type = dept_may.groupby('类型')['金额'].sum().round(2) if len(dept_may) > 0 else pd.Series()
            
            for type_name, june_amount in june_type.items():
                may_amount = may_type.get(type_name, 0)
                change = june_amount - may_amount
                print(f"  {type_name}: 6月{june_amount:,.2f}元, 5月{may_amount:,.2f}元, 变化{change:+,.2f}元")
    
    print()
    
    # 1.2 异常费用项目归因
    print("=" * 60)
    print("1.2 异常费用项目归因")
    print("=" * 60)
    
    print("【整体费用变化】")
    print(f"本期值(6月)：{june_total:,.2f}元")
    print(f"上期值(5月)：{may_total:,.2f}元")
    print(f"环比变化幅度：{change_rate:+.1f}%")
    print(f"变化值：{change_amount:+,.2f}元")
    print()
    
    # 按类别拆解异常费用
    print("【按类别拆解异常费用】")
    may_category_all = may_data.groupby('类别')['金额'].sum().round(2)
    june_category_all = june_data.groupby('类别')['金额'].sum().round(2)
    
    category_comparison = pd.DataFrame({
        '5月费用': may_category_all,
        '6月费用': june_category_all
    }).fillna(0)
    
    category_comparison['变化金额'] = category_comparison['6月费用'] - category_comparison['5月费用']
    category_comparison['环比变化率'] = (category_comparison['变化金额'] / category_comparison['5月费用'] * 100).replace([np.inf, -np.inf], 0)
    
    category_sorted = category_comparison.sort_values('变化金额', ascending=False)
    
    for category, row in category_sorted.iterrows():
        if row['变化金额'] != 0:
            print(f"{category}: 6月{row['6月费用']:,.2f}元, 5月{row['5月费用']:,.2f}元, 变化{row['变化金额']:+,.2f}元 ({row['环比变化率']:+.1f}%)")
    
    print()
    
    # 按配置项拆解异常费用（重点关注高金额项目）
    print("【按配置项拆解异常费用】")
    june_config = june_data.groupby('配置项')['金额'].sum().round(2).sort_values(ascending=False)
    may_config = may_data.groupby('配置项')['金额'].sum().round(2)
    
    print("6月主要配置项费用（TOP10）：")
    for config, june_amount in june_config.head(10).items():
        may_amount = may_config.get(config, 0)
        change = june_amount - may_amount
        print(f"  {config}: 6月{june_amount:,.2f}元, 5月{may_amount:,.2f}元, 变化{change:+,.2f}元")
    
    print()
    
    # 按地点拆解异常费用
    print("【按地点拆解异常费用】")
    june_location = june_data.groupby('地点')['金额'].sum().round(2).sort_values(ascending=False)
    may_location = may_data.groupby('地点')['金额'].sum().round(2)
    
    for location, june_amount in june_location.items():
        may_amount = may_location.get(location, 0)
        change = june_amount - may_amount
        print(f"  {location}: 6月{june_amount:,.2f}元, 5月{may_amount:,.2f}元, 变化{change:+,.2f}元")
    
    print()
    
    # 异常费用项目详细分析（重点关注大额费用）
    print("【异常费用项目详细分析】")
    
    # 找出6月最大的费用项目
    june_sorted = june_data.sort_values('金额', ascending=False)
    
    print("6月TOP10大额费用项目：")
    print(f"{'序号':<4} {'金额':<12} {'部门':<12} {'用户':<10} {'处理日期':<12} {'简短描述':<30}")
    print("-" * 90)
    
    for i, (_, row) in enumerate(june_sorted.head(10).iterrows(), 1):
        process_date = row['处理日期'].strftime('%Y-%m-%d')
        description = row['简短描述'][:28] + "..." if len(row['简短描述']) > 28 else row['简短描述']
        print(f"{i:<4} {row['金额']:>10,.0f} {row['部门']:<12} {row['用户']:<10} {process_date:<12} {description:<30}")
    
    print()
    
    # 重点标注无预算计划的异常增长项
    print("【⚠️ 无预算计划的异常增长项重点标注】")
    
    # 识别Finance部门的大额费用（通常这些是无预算的异常项目）
    finance_june = june_data[june_data['部门'] == 'Finance'].copy()
    
    if len(finance_june) > 0:
        print("🔴 Finance部门异常大额费用项目：")
        finance_sorted = finance_june.sort_values('金额', ascending=False)
        
        total_finance_june = finance_june['金额'].sum()
        print(f"Finance部门6月总费用：{total_finance_june:,.2f}元")
        print(f"占6月总费用比例：{total_finance_june/june_total*100:.1f}%")
        print()
        
        print("具体项目明细：")
        for _, row in finance_sorted.iterrows():
            process_date = row['处理日期'].strftime('%Y-%m-%d')
            create_date = row['创建时间'].strftime('%Y-%m-%d')
            print(f"• {row['金额']:>8,.0f}元 - {row['用户']} - {row['简短描述']}")
            print(f"  创建日期：{create_date}, 处理日期：{process_date}, 配置项：{row['配置项']}, 地点：{row['地点']}")
            print()
    
    # 识别其他异常增长项目
    print("🔴 其他部门异常增长项目：")
    
    # 找出6月单笔金额超过50000的非Finance部门项目
    high_amount_non_finance = june_data[(june_data['金额'] > 50000) & (june_data['部门'] != 'Finance')]
    
    if len(high_amount_non_finance) > 0:
        for _, row in high_amount_non_finance.sort_values('金额', ascending=False).iterrows():
            process_date = row['处理日期'].strftime('%Y-%m-%d')
            create_date = row['创建时间'].strftime('%Y-%m-%d')
            print(f"• {row['金额']:>8,.0f}元 - {row['部门']} - {row['用户']} - {row['简短描述']}")
            print(f"  创建日期：{create_date}, 处理日期：{process_date}")
    else:
        print("无其他部门异常大额费用项目")
    
    print()
    
    # 核心变化总结
    print("【核心变化总结】")
    print("1. 费用增长主要驱动因素：")
    
    # 分析主要增长来源
    if len(finance_june) > 0:
        finance_contribution = finance_june['金额'].sum() / change_amount * 100 if change_amount > 0 else 0
        print(f"   • Finance部门贡献了{finance_contribution:.1f}%的费用增长")
        
        # 分析时间集中度
        finance_dates = finance_june['创建时间'].dt.date.value_counts()
        concentrated_dates = finance_dates[finance_dates > 1]
        
        if len(concentrated_dates) > 0:
            print(f"   • 费用集中在{len(concentrated_dates)}个日期：", end="")
            for date, count in concentrated_dates.items():
                print(f"{date}({count}笔)", end=" ")
            print()
    
    print("2. 异常费用特征：")
    print(f"   • 单笔费用金额显著增大，6月平均单笔{june_data['金额'].mean():,.0f}元 vs 5月{may_data['金额'].mean():,.0f}元")
    print(f"   • 大额费用主要集中在基础设施投资和软件许可等项目")
    print(f"   • 费用审批和处理周期相对较短，显示为紧急或计划外支出")
    
    print()
    print("=" * 80)
    print("报告生成完成")
    print("=" * 80)

if __name__ == "__main__":
    generate_finance_attribution_report()

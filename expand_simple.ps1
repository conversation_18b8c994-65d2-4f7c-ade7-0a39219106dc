# 简单的数据扩展脚本
$existingContent = Get-Content 'flag-40.csv'
Write-Host "当前数据集包含 $($existingContent.Count - 1) 条记录"

# 配置数据
$departments = @('HR', 'IT', 'Sales', 'Customer Support', 'Product Management', 'Marketing', 'Operations')
$categories = @('Assets', 'Services', 'Travel', 'Miscellaneous')
$types = @('One-time', 'Recurring')
$statuses = @('Processed', 'Pending', 'Submitted', 'Declined')
$locations = @('Asia', 'Europe', 'North America', 'South America', 'Africa')
$configItems = @('server', 'cloud', 'database', 'software', 'monitoring', 'security', 'infrastructure', 'network', 'equipment', 'service', 'asset', 'travel', 'compliance')
$users = @('john_smith', 'mary_johnson', 'david_brown', 'sarah_davis', 'micha<PERSON>_wilson', 'lisa_anderson', 'robert_taylor', 'jennifer_thomas', 'william_jackson', 'elizabeth_white')

$descriptions = @(
    'System maintenance and upgrade request',
    'Software license renewal',
    'Hardware procurement',
    'Training and development program',
    'Consulting services engagement',
    'Travel and accommodation booking',
    'Office supplies and equipment',
    'Marketing campaign expenses',
    'Customer support tools',
    'Data backup and recovery'
)

# 生成新记录
$newRecords = @()
for ($i = 1; $i -le 1000; $i++) {
    if ($i % 100 -eq 0) {
        Write-Host "已生成 $i 条新记录..."
    }
    
    # 生成随机日期
    $createDate = Get-Date "2024-01-01"
    $createDate = $createDate.AddDays((Get-Random -Maximum 365))
    $createDate = $createDate.AddHours((Get-Random -Maximum 24))
    $createDate = $createDate.AddMinutes((Get-Random -Maximum 60))
    
    # 避开异常时间段
    while (($createDate.Month -eq 4 -and $createDate.Day -ge 8 -and $createDate.Day -le 10) -or 
           ($createDate.Month -eq 6 -and $createDate.Day -ge 10 -and $createDate.Day -le 12)) {
        $createDate = Get-Date "2024-01-01"
        $createDate = $createDate.AddDays((Get-Random -Maximum 365))
        $createDate = $createDate.AddHours((Get-Random -Maximum 24))
        $createDate = $createDate.AddMinutes((Get-Random -Maximum 60))
    }
    
    # 处理日期
    $processDays = Get-Random -Minimum 1 -Maximum 31
    $processDate = $createDate.AddDays($processDays)
    
    # 生成记录
    $guid1 = [System.Guid]::NewGuid().ToString()
    $guid2 = [System.Guid]::NewGuid().ToString()
    $amount = [Math]::Round((Get-Random -Minimum 1000 -Maximum 80000), 2)
    $processingCycle = [Math]::Max(1, ($processDate - $createDate).Days)
    
    $record = "$guid1,$($createDate.ToString('yyyy-MM-dd HH:mm:ss')),$amount,$($statuses | Get-Random),$($descriptions | Get-Random),$($configItems | Get-Random),$($users | Get-Random),$($departments | Get-Random),$($categories | Get-Random),$($processDate.ToString('yyyy-MM-dd HH:mm:ss')),$guid2,$($types | Get-Random),$($locations | Get-Random),$processingCycle"
    
    $newRecords += $record
}

Write-Host "成功生成 $($newRecords.Count) 条新记录"

# 合并数据
$allContent = $existingContent + $newRecords

# 写入文件
$allContent | Out-File 'flag-40.csv' -Encoding UTF8

Write-Host "数据集扩展完成！总记录数：$($allContent.Count - 1)"
Write-Host "异常数据已保持不变，新数据已添加到数据集中"

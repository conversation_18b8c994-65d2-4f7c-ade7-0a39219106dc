# 手动添加数据到CSV文件
$existingContent = Get-Content 'flag-40.csv'
Write-Host "当前数据集包含 $($existingContent.Count - 1) 条记录"

# 手动创建500条新记录
$newRecords = @()

# 生成500条新记录
for ($i = 1; $i -le 500; $i++) {
    $guid1 = [System.Guid]::NewGuid().ToString()
    $guid2 = [System.Guid]::NewGuid().ToString()
    
    # 随机日期（避开异常时间段）
    $year = 2024
    $month = Get-Random -Minimum 1 -Maximum 13
    $day = Get-Random -Minimum 1 -Maximum 29
    
    # 避开异常时间段
    while (($month -eq 4 -and $day -ge 8 -and $day -le 10) -or ($month -eq 6 -and $day -ge 10 -and $day -le 12)) {
        $month = Get-Random -Minimum 1 -Maximum 13
        $day = Get-Random -Minimum 1 -Maximum 29
    }
    
    $hour = Get-Random -Minimum 0 -Maximum 24
    $minute = Get-Random -Minimum 0 -Maximum 60
    $second = Get-Random -Minimum 0 -Maximum 60
    
    $createTime = "$year-$('{0:D2}' -f $month)-$('{0:D2}' -f $day) $('{0:D2}' -f $hour):$('{0:D2}' -f $minute):$('{0:D2}' -f $second)"
    
    # 处理时间（创建时间后1-30天）
    $processDays = Get-Random -Minimum 1 -Maximum 31
    $processDate = [DateTime]::Parse($createTime).AddDays($processDays)
    $processTime = $processDate.ToString("yyyy-MM-dd HH:mm:ss")
    
    # 随机数据
    $amount = [Math]::Round((Get-Random -Minimum 1000 -Maximum 80000), 2)
    $statuses = @('Processed', 'Pending', 'Submitted', 'Declined')
    $status = $statuses[(Get-Random -Maximum $statuses.Count)]
    
    $descriptions = @('System maintenance request', 'Software license renewal', 'Hardware procurement', 'Training program', 'Consulting services')
    $description = $descriptions[(Get-Random -Maximum $descriptions.Count)]
    
    $configItems = @('server', 'cloud', 'database', 'software', 'monitoring')
    $configItem = $configItems[(Get-Random -Maximum $configItems.Count)]
    
    $users = @('john_smith', 'mary_johnson', 'david_brown', 'sarah_davis', 'michael_wilson')
    $user = $users[(Get-Random -Maximum $users.Count)]
    
    $departments = @('HR', 'IT', 'Sales', 'Customer Support', 'Product Management')
    $department = $departments[(Get-Random -Maximum $departments.Count)]
    
    $categories = @('Assets', 'Services', 'Travel', 'Miscellaneous')
    $category = $categories[(Get-Random -Maximum $categories.Count)]
    
    $types = @('One-time', 'Recurring')
    $type = $types[(Get-Random -Maximum $types.Count)]
    
    $locations = @('Asia', 'Europe', 'North America', 'South America', 'Africa')
    $location = $locations[(Get-Random -Maximum $locations.Count)]
    
    $processingCycle = [Math]::Max(1, $processDays)
    
    # 创建记录
    $record = "$guid1,$createTime,$amount,$status,$description,$configItem,$user,$department,$category,$processTime,$guid2,$type,$location,$processingCycle"
    $newRecords += $record
    
    if ($i % 100 -eq 0) {
        Write-Host "已生成 $i 条新记录..."
    }
}

Write-Host "成功生成 $($newRecords.Count) 条新记录"

# 合并数据
$allContent = $existingContent + $newRecords

# 写入文件
$allContent | Set-Content 'flag-40.csv' -Encoding UTF8

Write-Host "数据集扩展完成！总记录数：$($allContent.Count - 1)"
Write-Host "异常数据已保持不变，新数据已添加到数据集中"

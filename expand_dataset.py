#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集扩展脚本
在保持异常数据不变的前提下，扩展数据集规模
"""

import csv
import random
import uuid
from datetime import datetime, timedelta
import json

# 配置参数
TARGET_RECORDS = 1000  # 要新增的记录数
DEPARTMENTS = ['HR', 'IT', 'Sales', 'Customer Support', 'Product Management', 'Marketing', 'Operations']
CATEGORIES = ['Assets', 'Services', 'Travel', 'Miscellaneous']
TYPES = ['One-time', 'Recurring']
STATUSES = ['Processed', 'Pending', 'Submitted', 'Declined']
LOCATIONS = ['Asia', 'Europe', 'North America', 'South America', 'Africa']
CONFIG_ITEMS = ['server', 'cloud', 'database', 'software', 'monitoring', 'security', 'infrastructure', 
                'network', 'equipment', 'service', 'asset', 'travel', 'compliance']

# 用户名池
USERS = ['john_smith', 'mary_johnson', 'david_brown', 'sarah_davis', 'michael_wilson', 'lisa_anderson',
         'robert_taylor', 'jennifer_thomas', 'william_jackson', 'elizabeth_white', 'james_harris',
         'patricia_martin', 'charles_thompson', 'linda_garcia', 'christopher_martinez', 'barbara_robinson',
         'daniel_clark', 'helen_rodriguez', 'matthew_lewis', 'nancy_lee', 'anthony_walker', 'karen_hall',
         'mark_allen', 'donna_young', 'steven_hernandez', 'carol_king', 'paul_wright', 'ruth_lopez',
         'andrew_hill', 'sharon_scott', 'joshua_green', 'michelle_adams', 'kenneth_baker', 'laura_gonzalez',
         'kevin_nelson', 'cynthia_carter', 'brian_mitchell', 'amy_perez', 'george_roberts', 'angela_turner']

def generate_uuid():
    """生成UUID格式的编号"""
    return str(uuid.uuid4())

def generate_random_datetime(start_date, end_date):
    """生成指定范围内的随机日期时间"""
    time_between = end_date - start_date
    days_between = time_between.days
    random_days = random.randrange(days_between)
    random_seconds = random.randrange(24 * 60 * 60)
    return start_date + timedelta(days=random_days, seconds=random_seconds)

def generate_processing_cycle(create_time, process_time):
    """计算处理周期"""
    delta = process_time - create_time
    return max(1, delta.days)  # 确保至少1天

def generate_amount():
    """生成合理的金额"""
    # 大部分金额在正常范围内，避免与异常数据冲突
    ranges = [
        (1000, 15000, 0.4),    # 小额
        (15000, 35000, 0.3),   # 中额
        (35000, 60000, 0.2),   # 大额
        (60000, 100000, 0.1)   # 超大额（但避免与Finance异常数据重叠）
    ]
    
    rand = random.random()
    cumulative = 0
    for min_amt, max_amt, prob in ranges:
        cumulative += prob
        if rand <= cumulative:
            return round(random.uniform(min_amt, max_amt), 2)
    
    return round(random.uniform(1000, 15000), 2)

def generate_description():
    """生成业务描述"""
    templates = [
        "System maintenance and upgrade request",
        "Software license renewal",
        "Hardware procurement",
        "Training and development program",
        "Consulting services engagement",
        "Travel and accommodation booking",
        "Office supplies and equipment",
        "Marketing campaign expenses",
        "Customer support tools",
        "Data backup and recovery",
        "Network infrastructure upgrade",
        "Security assessment and audit",
        "Business process optimization",
        "Employee benefits administration",
        "Vendor management services"
    ]
    return random.choice(templates)

def generate_record():
    """生成一条新记录"""
    # 创建时间：2024年全年，但避开异常数据的时间段
    start_date = datetime(2024, 1, 1)
    end_date = datetime(2024, 12, 31)
    
    # 避开异常时间段：4月8-10日和6月10-12日
    while True:
        create_time = generate_random_datetime(start_date, end_date)
        if not ((create_time.month == 4 and 8 <= create_time.day <= 10) or 
                (create_time.month == 6 and 10 <= create_time.day <= 12)):
            break
    
    # 处理时间：创建时间后1-30天
    process_days = random.randint(1, 30)
    process_time = create_time + timedelta(days=process_days)
    
    record = {
        '编号': generate_uuid(),
        '创建时间': create_time.strftime('%Y-%m-%d %H:%M:%S'),
        '金额': generate_amount(),
        '状态': random.choice(STATUSES),
        '简短描述': generate_description(),
        '配置项': random.choice(CONFIG_ITEMS),
        '用户': random.choice(USERS),
        '部门': random.choice(DEPARTMENTS),
        '类别': random.choice(CATEGORIES),
        '处理日期': process_time.strftime('%Y-%m-%d %H:%M:%S'),
        '来源ID': generate_uuid(),
        '类型': random.choice(TYPES),
        '地点': random.choice(LOCATIONS),
        '处理周期': generate_processing_cycle(create_time, process_time)
    }
    
    return record

def main():
    """主函数"""
    print("开始扩展数据集...")
    
    # 读取现有数据
    existing_data = []
    # 尝试不同的编码
    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
    for encoding in encodings:
        try:
            with open('flag-40.csv', 'r', encoding=encoding) as f:
                reader = csv.DictReader(f)
                for row in reader:
                    existing_data.append(row)
            print(f"成功使用 {encoding} 编码读取文件")
            break
        except UnicodeDecodeError:
            continue
    else:
        print("无法读取文件，尝试所有编码都失败")
    
    print(f"当前数据集包含 {len(existing_data)} 条记录")
    
    # 生成新数据
    new_records = []
    for i in range(TARGET_RECORDS):
        if i % 100 == 0:
            print(f"已生成 {i} 条新记录...")
        new_records.append(generate_record())
    
    print(f"成功生成 {len(new_records)} 条新记录")
    
    # 合并数据：保持前25条异常数据不变，在后面添加新数据
    all_data = existing_data + new_records
    
    # 写入新文件
    fieldnames = ['编号', '创建时间', '金额', '状态', '简短描述', '配置项', '用户', '部门', '类别', '处理日期', '来源ID', '类型', '地点', '处理周期']
    
    with open('flag-40.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(all_data)
    
    print(f"数据集扩展完成！总记录数：{len(all_data)}")
    print("异常数据已保持不变，新数据已添加到数据集中")

if __name__ == "__main__":
    main()

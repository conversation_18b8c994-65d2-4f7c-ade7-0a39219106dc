# 三个分析场景数据集说明

根据您的要求.md文档，我已经创建了三个独立的CSV数据集文件，每个文件对应一个特定的分析场景。

## 📁 数据集文件清单

### 1. scenario1_development_cycle.csv
**场景**：Development部门Q2处理周期异常分析  
**目标问题**：为什么Development部门在2024年Q2期间平均处理周期显著高于其他部门？

**数据特点**：
- 包含Q1和Q2各部门对比数据
- 重点突出4月第2周zhang_dev01用户的15个异常工单
- 处理周期从12天递增到38天，模拟工单积压效应
- 支持季度→月份→周→日的逐级下钻分析

### 2. finance_expense_analysis.csv  
**场景**：6月财务费用异常分析
**目标问题**：2024年6月的财务费用金额为什么显著高于2024年5月？

**数据特点**：
- 包含5月（对照期）和6月（观察期）各部门费用数据
- 重点突出6月第2周li_finance01用户的10笔大额费用
- 单笔金额167,800-305,000元，总计超过230万元
- 支持月度对比和部门拆解归因分析

### 3. approval_efficiency_analysis.csv
**场景**：审批效率周度分析  
**目标问题**：2024年4月10-16日期间的审批效率变化分析

**数据特点**：
- 包含上周（4月3-9日）和当周（4月10-16日）对比数据
- 展现各部门审批效率的环比变化
- 突出Development部门zhang_dev01用户对整体效率的影响
- 支持多维度效率分析和归因

## 🎯 分析路径示例

### 场景1分析路径：
```
1. 季度对比 → 确认Q2 Development部门异常
2. 月份下钻 → 定位4月为异常月份  
3. 周级分析 → 锁定4月第2周
4. 日期分析 → 识别4月8-10日集中提交
5. 工单归因 → zhang_dev01用户15个工单导致积压
```

### 场景2分析路径：
```
1. 月度对比 → 确认6月费用显著高于5月
2. 部门拆解 → Finance部门环比增长4,988%
3. 类别分析 → Assets和Services类各占约50%
4. 项目归因 → li_finance01用户大型投资项目
5. 时间集中 → 6月第2周集中发生
```

### 场景3分析路径：
```
1. 周度对比 → 当周效率下降172%
2. 部门分析 → Development部门异常（+517%）
3. 用户下钻 → zhang_dev01主要影响人员
4. 工单分析 → 10个大额基础设施工单
5. 资源归因 → 处理能力不足导致积压
```

## 📊 数据质量保证

### 数据一致性
- 所有文件保持相同的字段结构
- 时间格式统一：YYYY-MM-DD HH:MM:SS
- 处理周期计算准确（天数差）

### 业务逻辑合理性  
- 异常数据设计符合真实业务场景
- 金额设置合理，反映不同类型工单特点
- 用户行为模式真实可信

### 分析支持度
- 每个数据集都能完整支持对应的分析问题
- 提供充足的对照数据和异常数据
- 支持多维度下钻和归因分析

## 🔍 使用建议

1. **数据验证**：建议先对每个数据集进行基础统计分析
2. **分析工具**：可使用Excel、Python pandas或SQL进行分析
3. **可视化**：建议使用折线图展示趋势，表格展示明细
4. **扩展性**：如需更多数据支持，可基于现有模式继续添加

## 📋 预期分析结果

使用这三个数据集，您可以完整复现要求.md中描述的分析思路，并得到清晰的归因结论：

- **场景1**：zhang_dev01用户集中提交导致Development部门Q2处理周期异常
- **场景2**：li_finance01用户大额投资项目导致6月财务费用激增  
- **场景3**：Development部门工单积压影响整体审批效率

每个场景都提供了完整的分析链条和明确的归因结果。
